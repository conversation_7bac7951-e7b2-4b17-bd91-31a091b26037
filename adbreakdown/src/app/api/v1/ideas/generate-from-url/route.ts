import { NextRequest, NextResponse } from 'next/server';
import { GoogleGenerativeAI } from '@google/generative-ai';

export async function POST(request: NextRequest) {
  try {
    const { url } = await request.json();

    if (!url) {
      return NextResponse.json(
        { error: 'URL is required' },
        { status: 400 }
      );
    }

    console.log('Generating ideas for URL:', url);

    // Scrape the website content
    const websiteContent = await scrapeWebsite(url);
    
    // Generate ideas using Gemini AI
    const ideas = await generateIdeasWithAI(websiteContent, url);

    return NextResponse.json(ideas);
  } catch (error) {
    console.error('Error generating ideas:', error);
    return NextResponse.json(
      { error: 'Failed to generate ideas: ' + error.message },
      { status: 500 }
    );
  }
}

async function scrapeWebsite(url: string): Promise<string> {
  try {
    console.log('Scraping website:', url);
    
    const response = await fetch(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch website: ${response.status}`);
    }

    const html = await response.text();
    
    // Extract text content from HTML (simple approach)
    const textContent = html
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remove scripts
      .replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, '') // Remove styles
      .replace(/<[^>]+>/g, ' ') // Remove HTML tags
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim();

    // Extract key sections (title, meta description, etc.)
    const titleMatch = html.match(/<title[^>]*>([^<]+)<\/title>/i);
    const descriptionMatch = html.match(/<meta[^>]*name=["']description["'][^>]*content=["']([^"']+)["']/i);
    
    const title = titleMatch ? titleMatch[1] : '';
    const description = descriptionMatch ? descriptionMatch[1] : '';
    
    const processedContent = `
Title: ${title}
Description: ${description}
Content: ${textContent.substring(0, 2000)}
`.trim();

    console.log('Scraped content preview:', processedContent.substring(0, 300) + '...');
    return processedContent;
  } catch (error) {
    console.error('Error scraping website:', error);
    throw new Error('Failed to scrape website content');
  }
}

async function generateIdeasWithAI(websiteContent: string, url: string): Promise<any[]> {
  try {
    const apiKey = process.env.GEMINI_API_KEY;
    if (!apiKey) {
      throw new Error('Gemini API key not configured');
    }

    const genAI = new GoogleGenerativeAI(apiKey);
    const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });

    const prompt = `
Analyze the following website content and generate 2 distinct creative video advertisement concepts.

Website URL: ${url}
Website Content:
${websiteContent}

For each concept, provide a JSON object with the following structure:
{
  "summary": "A detailed 2-3 sentence narrative concept for a video ad",
  "hook": "A compelling one-liner that captures attention",
  "targetAudience": ["audience segment 1", "audience segment 2", "audience segment 3"],
  "USPs": ["unique selling point 1", "unique selling point 2", "unique selling point 3", "unique selling point 4"]
}

Requirements:
- Base the concepts on the actual content and offerings of the website
- Make each concept distinct and creative
- Focus on visual storytelling suitable for 8-10 second video ads
- Target modern consumers and professionals
- Extract real USPs and value propositions from the website content
- If this is an e-commerce site, focus on the products/services offered
- If this is a service site, focus on the benefits and solutions provided

Return a JSON array with exactly 2 concepts.
`;

    console.log('Sending prompt to Gemini AI...');
    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();
    
    console.log('Gemini AI response:', text.substring(0, 300) + '...');

    // Try to parse the JSON response
    try {
      // Extract JSON from the response (in case it's wrapped in markdown)
      const jsonMatch = text.match(/\[[\s\S]*\]/);
      const jsonText = jsonMatch ? jsonMatch[0] : text;
      
      const ideas = JSON.parse(jsonText);
      
      if (!Array.isArray(ideas) || ideas.length === 0) {
        throw new Error('Invalid response format from AI');
      }

      return ideas;
    } catch (parseError) {
      console.error('Failed to parse AI response as JSON:', parseError);
      throw new Error('Failed to parse AI response');
    }
  } catch (error) {
    console.error('Error with AI generation:', error);
    throw error;
  }
}