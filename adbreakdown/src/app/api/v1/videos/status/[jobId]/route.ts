import { NextRequest, NextResponse } from 'next/server';

// Import the job storage from the generation endpoint
// In production, this would be a shared database
const jobStorage = new Map<string, any>();

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ jobId: string }> }
) {
  try {
    const { jobId } = await params;

    if (!jobId) {
      return NextResponse.json(
        { error: 'Job ID is required' },
        { status: 400 }
      );
    }

    // Check if this is a Veo job (starts with 'veo_')
    if (jobId.startsWith('veo_')) {
      // Try to get job from the generation endpoint's storage
      // This is a workaround since we can't directly import the storage
      // In production, use a shared database like Redis or PostgreSQL
      
      // For now, simulate the Veo generation process
      const timestamp = jobId.split('_')[1];
      const jobAge = Date.now() - parseInt(timestamp);
      
      if (jobAge < 10000) {
        return NextResponse.json({
          status: 'pending',
          progress: 5,
          message: 'Initializing Veo 3 video generation...'
        });
      } else if (jobAge < 20000) {
        return NextResponse.json({
          status: 'processing',
          progress: 20,
          message: 'Converting script to video prompt...'
        });
      } else if (jobAge < 40000) {
        return NextResponse.json({
          status: 'processing',
          progress: 50,
          message: 'Calling Veo 3 API...'
        });
      } else if (jobAge < 60000) {
        return NextResponse.json({
          status: 'processing',
          progress: 80,
          message: 'Generating high-quality video with Veo 3...'
        });
      } else {
        return NextResponse.json({
          status: 'completed',  
          progress: 100,
          message: 'Veo 3 video generation completed!',
          videoUrl: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4'
        });
      }
    }

    // Fallback for old job format - mock video generation status
    const timestamp = jobId.split('_')[1];
    const jobAge = Date.now() - parseInt(timestamp);
    
    // Simulate different stages based on job age
    if (jobAge < 5000) {
      return NextResponse.json({
        status: 'pending',
        progress: 10,
        message: 'Initializing video generation...'
      });
    } else if (jobAge < 15000) {
      return NextResponse.json({
        status: 'processing',
        progress: 45,
        message: 'Generating voiceover audio...'
      });
    } else if (jobAge < 25000) {
      return NextResponse.json({
        status: 'processing',
        progress: 75,
        message: 'Composing video scenes...'
      });
    } else if (jobAge < 35000) {
      return NextResponse.json({
        status: 'processing',
        progress: 90,
        message: 'Finalizing video render...'
      });
    } else {
      // Job completed - return mock video URL
      return NextResponse.json({
        status: 'completed',
        progress: 100,
        message: 'Video generation completed!',
        videoUrl: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4'
      });
    }
  } catch (error) {
    console.error('Error checking video status:', error);
    return NextResponse.json(
      { error: 'Failed to check video status' },
      { status: 500 }
    );
  }
}