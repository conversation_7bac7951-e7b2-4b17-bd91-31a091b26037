import { NextRequest, NextResponse } from 'next/server';
import { getJob } from '@/lib/jobStorage';

export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ jobId: string }> }
) {
  try {
    const { jobId } = await params;

    if (!jobId) {
      return NextResponse.json(
        { error: 'Job ID is required' },
        { status: 400 }
      );
    }

    // Get job status from shared storage
    const job = getJob(jobId);

    if (!job) {
      return NextResponse.json(
        { error: 'Job not found' },
        { status: 404 }
      );
    }

    // Return the actual job status
    return NextResponse.json({
      status: job.status,
      progress: job.progress,
      message: job.message,
      videoUrl: job.videoUrl || null,
      operationName: job.operationName || null
    });


  } catch (error) {
    console.error('Error checking video status:', error);
    return NextResponse.json(
      { error: 'Failed to check video status' },
      { status: 500 }
    );
  }
}