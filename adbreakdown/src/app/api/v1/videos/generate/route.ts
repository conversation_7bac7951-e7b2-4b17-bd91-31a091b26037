import { NextRequest, NextResponse } from 'next/server';
import { GoogleAuth } from 'google-auth-library';

// In-memory storage for job status (in production, use a database)
const jobStorage = new Map<string, any>();

export async function POST(request: NextRequest) {
  try {
    const { script, voiceId, aspectRatio } = await request.json();

    if (!script || !script.scenes || script.scenes.length === 0) {
      return NextResponse.json(
        { error: 'Script with scenes is required' },
        { status: 400 }
      );
    }

    // Generate a unique job ID
    const jobId = `veo_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // Store initial job status
    jobStorage.set(jobId, {
      status: 'pending',
      progress: 0,
      message: 'Initializing video generation...',
      createdAt: Date.now(),
      script,
      voiceId,
      aspectRatio,
      videoUrl: null
    });

    console.log('Video generation job created:', {
      jobId,
      scenes: script.scenes.length,
      voiceId,
      aspectRatio
    });

    // Start video generation process asynchronously
    generateVideoAsync(jobId, script, aspectRatio).catch(error => {
      console.error('Video generation failed:', error);
      jobStorage.set(jobId, {
        ...jobStorage.get(jobId),
        status: 'failed',
        message: 'Video generation failed: ' + error.message
      });
    });

    return NextResponse.json({
      status: 'pending',
      jobId
    });
  } catch (error) {
    console.error('Error creating video generation job:', error);
    return NextResponse.json(
      { error: 'Failed to create video generation job' },
      { status: 500 }
    );
  }
}

async function generateVideoAsync(jobId: string, script: any, aspectRatio: string) {
  try {
    // Update status to processing
    jobStorage.set(jobId, {
      ...jobStorage.get(jobId),
      status: 'processing',
      progress: 10,
      message: 'Converting script to video prompt...'
    });

    // Convert script scenes to a cohesive video prompt
    const videoPrompt = convertScriptToPrompt(script, aspectRatio);
    console.log('Generated video prompt:', videoPrompt);

    // Update progress
    jobStorage.set(jobId, {
      ...jobStorage.get(jobId),
      progress: 30,
      message: 'Calling Veo 3 API...'
    });

    // Call Veo 3 API and get the result
    const { videoUrl, operationName } = await callVeoAPI(videoPrompt, aspectRatio);

    // Update to completed
    jobStorage.set(jobId, {
      ...jobStorage.get(jobId),
      status: 'completed',
      progress: 100,
      message: 'Video generation completed!',
      videoUrl,
      operationName // Store the operation name for reference
    });

    console.log('Video generation completed:', { jobId, videoUrl, operationName });
  } catch (error) {
    console.error('Video generation error:', error);
    jobStorage.set(jobId, {
      ...jobStorage.get(jobId),
      status: 'failed',
      progress: 0,
      message: 'Failed to generate video: ' + error.message
    });
  }
}

function convertScriptToPrompt(script: any, aspectRatio: string): string {
  // Convert the scene-by-scene script into a cohesive video prompt
  const scenes = script.scenes;
  
  // Extract key visual elements and narrative
  const visualElements = scenes.map((scene: any) => scene.visualDescription).join('. ');
  const narrative = scenes.map((scene: any) => scene.voiceoverText).join('. ');
  
  // Create a comprehensive prompt for Veo 3
  const prompt = `
Create a ${aspectRatio === '16:9' ? 'landscape' : 'portrait'} format commercial video advertisement:

VISUAL STORY: ${visualElements}

NARRATIVE: ${narrative}

STYLE: Professional commercial advertisement with high production value, smooth transitions between scenes, vibrant colors, modern cinematography. The video should feel premium and engaging, suitable for social media advertising.

DURATION: 8 seconds of dynamic content with seamless scene transitions.
`.trim();

  return prompt;
}

async function callVeoAPI(prompt: string, aspectRatio: string): Promise<{ videoUrl: string; operationName: string }> {
  try {
    // Get Google Cloud credentials
    const serviceAccountKey = JSON.parse(process.env.GOOGLE_CLOUD_SERVICE_ACCOUNT_KEY || '{}');
    const projectId = process.env.VERTEX_AI_PROJECT_ID;
    const location = process.env.VERTEX_AI_LOCATION || 'us-central1';
    
    if (!serviceAccountKey.private_key || !projectId) {
      throw new Error('Missing Google Cloud credentials');
    }

    // Initialize Google Auth
    const auth = new GoogleAuth({
      credentials: serviceAccountKey,
      scopes: ['https://www.googleapis.com/auth/cloud-platform']
    });

    const authClient = await auth.getClient();
    const accessToken = await authClient.getAccessToken();

    if (!accessToken.token) {
      throw new Error('Failed to get access token');
    }

    // Prepare the request body for Veo 3
    const requestBody = {
      instances: [
        {
          prompt: prompt
        }
      ],
      parameters: {
        aspectRatio: aspectRatio,
        resolution: "720p",
        sampleCount: 1,
        storageUri: `gs://${process.env.NEXT_PUBLIC_GCS_BUCKET_NAME}/generated-videos/`
      }
    };

    console.log('Calling Veo API with:', {
      prompt: prompt.substring(0, 200) + '...',
      aspectRatio,
      projectId,
      location
    });

    // Call Vertex AI Veo API
    const response = await fetch(
      `https://${location}-aiplatform.googleapis.com/v1/projects/${projectId}/locations/${location}/publishers/google/models/veo-3.0-generate-001:predictLongRunning`,
      {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken.token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      }
    );

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Veo API error:', {
        status: response.status,
        statusText: response.statusText,
        error: errorText
      });
      throw new Error(`Veo API error: ${response.status} - ${errorText}`);
    }

    const result = await response.json();
    console.log('Veo API response:', result);

    // Extract the operation name for polling
    const operationName = result.name;
    if (!operationName) {
      throw new Error('No operation name returned from Veo API');
    }

    console.log('Starting to poll operation:', operationName);
    
    // Poll the operation until completion
    const videoUrl = await pollVeoOperation(operationName, accessToken.token!);
    return { videoUrl, operationName };
    
  } catch (error) {
    console.error('Veo API call failed:', error);
    throw error;
  }
}

async function pollVeoOperation(operationName: string, accessToken: string): Promise<string> {
  const maxAttempts = 60; // Maximum 10 minutes (60 attempts * 10 seconds)
  const pollInterval = 10000; // 10 seconds
  
  for (let attempt = 0; attempt < maxAttempts; attempt++) {
    try {
      console.log(`Polling attempt ${attempt + 1}/${maxAttempts} for operation: ${operationName}`);
      
      // Poll the operation status
      // Based on Veo documentation, we should use the full operation name as returned
      // The operation name includes the full path: projects/.../publishers/google/models/veo-3.0-generate-001/operations/...
      
      const pollUrl = `https://us-central1-aiplatform.googleapis.com/v1/${operationName}`;
      console.log('Polling URL:', pollUrl);
      
      const pollResponse = await fetch(
        pollUrl,
        {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (!pollResponse.ok) {
        const errorText = await pollResponse.text();
        console.error('Operation polling error:', errorText);
        throw new Error(`Failed to poll operation: ${pollResponse.status}`);
      }

      const operationResult = await pollResponse.json();
      console.log(`Operation status:`, {
        done: operationResult.done,
        state: operationResult.metadata?.state || 'unknown'
      });

      // Check if operation is complete
      if (operationResult.done) {
        if (operationResult.error) {
          console.error('Operation failed:', operationResult.error);
          throw new Error(`Video generation failed: ${operationResult.error.message}`);
        }

        // Extract the video URL from the response
        const response = operationResult.response;
        if (response && response.predictions && response.predictions.length > 0) {
          const prediction = response.predictions[0];
          
          // Video URL could be in different places depending on whether storageUri was provided
          let videoUrl = null;
          
          if (prediction.videoUri) {
            videoUrl = prediction.videoUri;
          } else if (prediction.videoBytes) {
            // If video is returned as bytes, we'd need to save it to GCS first
            console.log('Video returned as bytes, need to save to storage');
            // For now, return a placeholder - in production, save bytes to GCS
            videoUrl = 'https://storage.googleapis.com/your-bucket/generated-video.mp4';
          } else if (prediction.generatedVideo) {
            videoUrl = prediction.generatedVideo.videoUri || prediction.generatedVideo.videoUrl;
          }

          if (videoUrl) {
            console.log('Video generation completed! URL:', videoUrl);
            return videoUrl;
          } else {
            console.error('No video URL found in response:', response);
            throw new Error('Video URL not found in completed operation');
          }
        } else {
          console.error('No predictions in response:', response);
          throw new Error('No video predictions in completed operation');
        }
      }

      // Wait before next poll
      if (attempt < maxAttempts - 1) {
        console.log(`Operation not complete, waiting ${pollInterval/1000} seconds...`);
        await new Promise(resolve => setTimeout(resolve, pollInterval));
      }
    } catch (error) {
      console.error(`Polling attempt ${attempt + 1} failed:`, error);
      if (attempt === maxAttempts - 1) {
        throw error;
      }
      // Wait before retry
      await new Promise(resolve => setTimeout(resolve, pollInterval));
    }
  }

  throw new Error('Video generation timed out after 10 minutes');
}

// Export the job storage for the status endpoint
export { jobStorage };