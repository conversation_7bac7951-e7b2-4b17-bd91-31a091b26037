'use client'
import React, { useState, useEffect, useRef } from 'react'
import Link from "next/link"
import Image from "next/image"
import { usePathname } from "next/navigation"
import { useUser, useClerk } from "@clerk/nextjs"
import { But<PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { 
  Home,
  BarChart3,
  Calendar,
  Library,
  Settings,
  HelpCircle,
  User,
  CreditCard,
  LogOut,
  ChevronUp,
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  Menu,
  X,
  Slash
} from "lucide-react"
import { cn } from "@/lib/utils"

// Navigation items for the app sidebar
const navItems = [
  {
    title: "Studio",
    href: "/studio",
    icon: Home,
    description: "Dashboard overview"
  },
  {
    title: "Optimise",
    href: "/studio/optimise",
    icon: BarChart3,
    description: "Private ad analysis"
  },
  {
    title: "Create",
    href: "/studio/create",
    icon: CreditCard,
    description: "AI-powered ad creation",
    comingSoon: true
  },
  {
    title: "Plan",
    href: "/studio/plan",
    icon: Calendar,
    description: "Campaign planning",
    comingSoon: true
  },
  {
    title: "Explore",
    href: "/studio/explore",
    icon: Library,
    description: "Discover trends",
    comingSoon: true
  },
  {
    title: "Brand",
    href: "/studio/brand",
    icon: User,
    description: "Brand management"
  },
  {
    title: "Ad Library",
    href: "/ad-library",
    icon: Library,
    description: "Ad Library"
  }
]

// Function to get page info based on pathname
function getPageInfo(pathname: string, brandName?: string) {
  const pathSegments = pathname.split('/').filter(Boolean)
  
  // Map pathnames to page info
  const pageMap: Record<string, { title: string; description: string; icon?: any }> = {
    '/studio': { 
      title: 'Studio', 
      description: 'Your portal to AI-powered ad analysis and creation',
      icon: Home
    },
    '/studio/optimise': { 
      title: 'Optimise', 
      description: 'Get optimization recommendations for your ad assets before launching with our advanced AI',
      icon: BarChart3
    },
    '/studio/create': { 
      title: 'Create', 
      description: 'AI-powered ad creation',
      icon: CreditCard
    },
    '/studio/create2': { 
      title: 'AI Video Generator', 
      description: 'Create compelling video ads with AI from product URLs',
      icon: CreditCard
    },
    '/studio/plan': { 
      title: 'Plan', 
      description: 'Campaign planning',
      icon: Calendar
    },
    '/studio/explore': { 
      title: 'Explore', 
      description: 'Discover trends',
      icon: Library
    },
    '/studio/brand': { 
      title: 'Brand', 
      description: 'Brand management',
      icon: User
    },
    '/ad-library': { 
      title: 'Ad Library', 
      description: 'Browse and analyze ad creatives',
      icon: Library
    },
    '/billing': { 
      title: 'Billing', 
      description: 'Manage your subscription and billing',
      icon: CreditCard
    },
    '/settings': { 
      title: 'Settings', 
      description: 'Account and application settings',
      icon: Settings
    },
    '/help': { 
      title: 'Help', 
      description: 'Get support and documentation',
      icon: HelpCircle
    }
  }

  // Check for exact match first
  if (pageMap[pathname]) {
    return pageMap[pathname]
  }

  // Check for dynamic routes (like /studio/private/[slug])
  if (pathname.startsWith('/studio/private/')) {
    return { 
      title: 'Pre-launch Analysis', 
      description: 'Confidential analysis for pre-launch optimization',
      icon: BarChart3
    }
  }

  // Check for brand profile routes
  if (pathname.startsWith('/studio/brand/') && pathname !== '/studio/brand' && pathname !== '/studio/brand/create') {
    const segments = pathname.split('/')
    if (segments.length >= 4) {
      const isEdit = segments[4] === 'edit'
      return { 
        title: `Brand Profile › ${brandName || 'Loading...'}`, 
        description: isEdit ? 'Edit brand profile settings' : 'View and manage brand profile',
        icon: User
      }
    }
  }

  // Default fallback
  return { 
    title: pathSegments[pathSegments.length - 1]?.charAt(0).toUpperCase() + pathSegments[pathSegments.length - 1]?.slice(1) || 'Dashboard',
    description: 'Navigate your workspace',
    icon: Home
  }
}

// Dashboard Header Component
function DashboardHeader({ isSidebarCollapsed = false }: { isSidebarCollapsed?: boolean }) {
  const pathname = usePathname()
  const { user } = useUser()
  const [brandName, setBrandName] = useState<string>('')
  
  // Fetch brand name if on brand profile page
  useEffect(() => {
    if (pathname.startsWith('/studio/brand/') && pathname !== '/studio/brand' && pathname !== '/studio/brand/create') {
      const segments = pathname.split('/')
      const brandId = segments[3]
      
      if (brandId) {
        fetch(`/api/brands/${brandId}`)
          .then(res => res.json())
          .then(data => {
            if (data.brand?.brand_name) {
              setBrandName(data.brand.brand_name)
            }
          })
          .catch(console.error)
      }
    } else {
      setBrandName('')
    }
  }, [pathname])
  
  const pageInfo = getPageInfo(pathname, brandName)
  
  return (
    <Card className="rounded-none bg-background sticky top-0 z-50 border-0 shadow-none">
      <div className="flex h-10 items-center justify-between px-3 py-0">
        {/* Left section - Brand/Logo with Breadcrumb */}
        <div className="flex items-center gap-2">
          <Link href="/studio" className="flex items-center gap-2 hover:opacity-80 transition-opacity">
            <Image alt="Breakdown.ad Logo" src="/logo.png" width={24} height={24} className="rounded-sm" />
            <span className={`text-xl mx-2 font-semibold tracking-tight bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent transition-all duration-300 overflow-hidden ${
              isSidebarCollapsed ? 'w-0 opacity-0' : 'w-auto opacity-100'
            }`}>
              Breakdown.ad
            </span>
          </Link>
          
          {/* Breadcrumb */}
          {pathname !== '/studio' && (
            <div className="flex items-center">
              <Slash className="h-4 w-4 text-muted-foreground mx-2" />
              <span className="text-xl mx-2 font-semibold tracking-tight bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                {pageInfo.title}
              </span>
            </div>
          )}
        </div>
        
        {/* Right section - User Menu */}
        <div className="flex items-center gap-4">
          {/* User controls can be added here if needed */}
        </div>
      </div>
    </Card>
  )
}

// Dashboard Sidebar Component
function DashboardSidebar({ onCollapseChange }: { onCollapseChange?: (isCollapsed: boolean) => void }) {
  const { user } = useUser()
  const { signOut } = useClerk()
  const pathname = usePathname()
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [isHovered, setIsHovered] = useState(false)
  const sidebarRef = useRef<HTMLDivElement>(null)
  
  // Hover handlers
  const handleMouseEnter = () => setIsHovered(true)
  const handleMouseLeave = () => setIsHovered(false)
  
  // Visual expansion logic
  const isVisuallyExpanded = !isCollapsed || isHovered
  
  // Toggle functions
  const toggleMobileMenu = () => setIsMobileMenuOpen(open => !open)
  const closeMobileMenu = () => setIsMobileMenuOpen(false)
  const toggleCollapse = () => {
    const newState = !isCollapsed
    setIsCollapsed(newState)
    localStorage.setItem("sidebarCollapsed", String(newState))
  }
  
  // Load collapsed state from localStorage
  useEffect(() => {
    const saved = localStorage.getItem("sidebarCollapsed")
    if (saved !== null) {
      setIsCollapsed(saved === "true")
    }
  }, [])
  
  // Notify parent of collapse changes
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (onCollapseChange) {
        onCollapseChange(isCollapsed)
      }
    }, 0)
    return () => clearTimeout(timeoutId)
  }, [isCollapsed, onCollapseChange])
  
  const sidebarContent = (
    <>
      {/* Scrollable content area */}
      <div className="flex flex-1 flex-col overflow-y-auto">
        {/* Navigation list */}
        <ul className={cn("flex flex-col w-full px-2 pt-16", isVisuallyExpanded ? "gap-2" : "gap-4")}>
          {navItems.map((item) => {
            const active = pathname === item.href || (pathname?.startsWith(item.href) && item.href !== "/studio")
            return (
              <li
                key={item.href}
                className={cn(
                  "w-full font-medium transition-colors",
                  active && isVisuallyExpanded ? "bg-primary/10" : "hover:bg-muted",
                  "rounded-lg"
                )}
              >
                {item.comingSoon ? (
                  <div className={cn(
                    "group flex items-center gap-2 rounded-[inherit] opacity-50 cursor-not-allowed",
                    isVisuallyExpanded ? "justify-start p-2" : "justify-center p-0"
                  )}>
                    <item.icon className={cn(
                      "size-6 transition-colors",
                      isVisuallyExpanded ? "stroke-[1.5px]" : "stroke-[2px]"
                    )} />
                    {isVisuallyExpanded && (
                      <>
                        <span className="truncate text-sm">{item.title}</span>
                        <Badge variant="secondary" className="text-xs px-2 py-0.5 bg-orange-50 text-orange-700 border-orange-200 ml-auto">
                          Coming Soon
                        </Badge>
                      </>
                    )}
                  </div>
                ) : (
                  <Link
                    href={item.href}
                    onClick={closeMobileMenu}
                    aria-current={active ? "page" : undefined}
                    className={cn(
                      "group flex items-center gap-2 rounded-[inherit]",
                      isVisuallyExpanded ? "justify-start p-2" : "justify-center p-0",
                      active ? "text-primary" : "text-muted-foreground hover:text-primary"
                    )}
                  >
                    <item.icon className={cn(
                      "size-6 transition-colors",
                      isVisuallyExpanded ? "stroke-[1.5px]" : "stroke-[2px]",
                      active ? "fill-current stroke-none" : "fill-none stroke-current group-hover:fill-current group-hover:stroke-none"
                    )} />
                    {isVisuallyExpanded && <span className="truncate text-sm">{item.title}</span>}
                  </Link>
                )}
              </li>
            )
          })}
        </ul>
      </div>
      
      {/* Fixed bottom section */}
      <div className="mt-auto">
        {/* Collapse Toggle */}
        <div className="flex justify-center py-2">
          <Button 
            variant="outline" 
            size="icon" 
            className="size-8 rounded-md hover:bg-muted" 
            aria-label={isCollapsed ? "Expand sidebar" : "Collapse sidebar"} 
            onClick={toggleCollapse}
          >
            {isCollapsed ? (
              <ChevronRight className="h-4 w-4" />
            ) : (
              <ChevronLeft className="h-4 w-4" />
            )}
          </Button>
        </div>
        
        {/* Account Dropdown */}
        <div className={cn("mt-1 px-2 pb-20", isCollapsed && "flex justify-center")}>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <div
                className={cn(
                  "group flex cursor-pointer items-center rounded-full p-2 hover:bg-muted",
                  isCollapsed ? "justify-center" : "justify-start"
                )}
                aria-label="Account menu"
              >
                <Avatar className={cn("h-7 w-7 shrink-0", isCollapsed ? "mr-0" : "mr-2")}>
                  <AvatarImage src={user?.imageUrl} alt={user?.fullName || "User Avatar"} />
                  <AvatarFallback className="bg-foreground text-xs font-medium text-background">
                    {user?.fullName?.charAt(0)?.toUpperCase() ?? "U"}
                  </AvatarFallback>
                </Avatar>
                {!isCollapsed && (
                  <>
                    <span className="flex-1 truncate text-sm font-medium">
                      {user?.fullName ?? "User Name"}
                    </span>
                    <ChevronDown className="ml-1.5 h-4 w-4 shrink-0 text-muted-foreground transition-transform group-hover:rotate-180" />
                  </>
                )}
              </div>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuItem asChild>
                <Link href="/billing" onClick={closeMobileMenu}>
                  <CreditCard className="mr-2 h-4 w-4" />
                  <span>Billing</span>
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href="/settings" onClick={closeMobileMenu}>
                  <Settings className="mr-2 h-4 w-4" />
                  <span>Settings</span>
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href="/help" onClick={closeMobileMenu}>
                  <HelpCircle className="mr-2 h-4 w-4" />
                  <span>Help</span>
                </Link>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => signOut()}>
                <LogOut className="mr-2 h-4 w-4" />
                <span>Log out</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </>
  )
  
  return (
    <div>
      {/* Mobile toggle */}
      <Button 
        variant="ghost" 
        size="icon" 
        className="fixed left-4 top-4 z-50 rounded-full md:hidden" 
        onClick={toggleMobileMenu} 
        aria-label={isMobileMenuOpen ? "Close menu" : "Open menu"} 
        aria-expanded={isMobileMenuOpen}
      >
        {isMobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
      </Button>
      
      {/* Mobile sidebar panel */}
      <div 
        role="dialog" 
        aria-modal="true" 
        className={cn(
          "fixed inset-y-0 left-0 z-40 flex h-full w-64 transform flex-col bg-background shadow-lg transition-transform duration-300 ease-in-out md:hidden overflow-hidden", 
          isMobileMenuOpen ? "translate-x-0" : "-translate-x-full"
        )}
      >
        {sidebarContent}
      </div>
      
      {/* Overlay for mobile */}
      {isMobileMenuOpen && (
        <div 
          className="fixed inset-0 z-30 bg-black/30 md:hidden" 
          onClick={closeMobileMenu} 
          aria-hidden="true" 
        />
      )}
      
      {/* Desktop sidebar */}
      <aside 
        ref={sidebarRef}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        className={cn(
          "fixed top-0 left-0 z-10 hidden h-screen flex-col rounded-none p-0 transition-all duration-300 md:flex overflow-hidden bg-background", 
          isVisuallyExpanded ? "w-60" : "w-12"
        )}
      >
        {sidebarContent}
      </aside>
    </div>
  )
}

// Dashboard Shell Component
function DashboardShell({ children, isSidebarCollapsed }: { children: React.ReactNode; isSidebarCollapsed?: boolean }) {
  const pathname = usePathname()
  const isCreatePage = pathname === '/studio/create'
  
  return (
    <div className={cn(
      "flex flex-col", 
      "transition-all duration-300",
      isSidebarCollapsed ? "md:ml-12" : "md:ml-60"
    )}>
      <div className="h-[calc(100vh-40px-0.5rem)] mt-0 ml-0 mr-2 mb-2 rounded-2xl border bg-white shadow-sm overflow-hidden">
        <div className={cn(
          "h-full overflow-y-auto",
          isCreatePage ? "p-0" : "p-6"
        )}>
          {children}
        </div>
      </div>
    </div>
  )
}

export default function AppLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false)
  
  return (
    <div className="flex h-screen bg-background">
      <DashboardSidebar onCollapseChange={setIsSidebarCollapsed} />
      <div className="flex-1 flex flex-col bg-background">
        <DashboardHeader isSidebarCollapsed={isSidebarCollapsed} />
        <DashboardShell isSidebarCollapsed={isSidebarCollapsed}>
          {children}
        </DashboardShell>
      </div>
    </div>
  )
}