'use client';

import { useState } from 'react';
import ChatInterface from '@/components/studio/ChatInterface';
import { VideoGenerationState } from '@/types/studio';

const Create2Page = () => {
  const [state, setState] = useState<VideoGenerationState>({
    step: 'init',
    url: '',
    ideas: [],
    selectedIdea: null,
    duration: null,
    voice: null,
    aspectRatio: null,
    script: null,
    jobId: null,
    videoUrl: null,
    status: 'idle'
  });

  return (
    <div className="h-full">
      <ChatInterface state={state} setState={setState} />
    </div>
  );
};

export default Create2Page;