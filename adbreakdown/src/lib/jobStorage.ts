// Shared job storage for video generation
// In production, replace this with Redis or a database
export const jobStorage = new Map<string, any>();

export interface JobStatus {
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;
  message: string;
  createdAt: number;
  script?: any;
  voiceId?: string;
  aspectRatio?: string;
  videoUrl?: string | null;
  operationName?: string;
}

export function getJob(jobId: string): JobStatus | undefined {
  return jobStorage.get(jobId);
}

export function setJob(jobId: string, status: JobStatus): void {
  jobStorage.set(jobId, status);
}

export function updateJob(jobId: string, updates: Partial<JobStatus>): void {
  const existing = jobStorage.get(jobId);
  if (existing) {
    jobStorage.set(jobId, { ...existing, ...updates });
  }
}

export function deleteJob(jobId: string): void {
  jobStorage.delete(jobId);
}
