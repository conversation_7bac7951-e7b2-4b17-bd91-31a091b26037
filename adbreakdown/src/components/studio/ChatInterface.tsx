'use client';

import { useState, useEffect, useRef } from 'react';
import { ChatMessage, VideoGenerationState, VideoIdea, Voice, AspectRatio } from '@/types/studio';
import URLInputForm from './URLInputForm';
import IdeaCard from './IdeaCard';
import ParameterSelectors from './ParameterSelectors';
import ScriptEditor from './ScriptEditor';
import FinalReview from './FinalReview';
import { Bo<PERSON>, User } from 'lucide-react';

interface ChatInterfaceProps {
  state: VideoGenerationState;
  setState: React.Dispatch<React.SetStateAction<VideoGenerationState>>;
}

const ChatInterface: React.FC<ChatInterfaceProps> = ({ state, setState }) => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputValue, setInputValue] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  // Store values in refs to persist across re-renders
  const selectedIdeaRef = useRef<VideoIdea | null>(null);
  const durationRef = useRef<number | null>(null);
  const voiceRef = useRef<Voice | null>(null);
  const aspectRatioRef = useRef<AspectRatio | null>(null);
  const scriptRef = useRef<any | null>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    if (messages.length === 0) {
      addBotMessage(
        "Hello! I'm here to help you create an amazing video ad. Let's start by getting your product URL. What product would you like to create an ad for?",
        <URLInputForm onSubmit={handleUrlSubmit} />
      );
    }
  }, []);

  const addBotMessage = (text: string, component?: React.ReactNode) => {
    const message: ChatMessage = {
      id: `bot-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      text,
      sender: 'bot',
      component,
      timestamp: new Date(),
    };
    setMessages(prev => [...prev, message]);
  };

  const addUserMessage = (text: string) => {
    const message: ChatMessage = {
      id: `user-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      text,
      sender: 'user',
      timestamp: new Date(),
    };
    setMessages(prev => [...prev, message]);
  };

  const handleUrlSubmit = async (url: string) => {
    addUserMessage(url);
    setState(prev => ({ ...prev, url, step: 'generating-ideas' }));
    
    addBotMessage("Great! Let me analyze your product and generate some creative concepts...");
    
    try {
      const response = await fetch('/api/v1/ideas/generate-from-url', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ url }),
      });
      
      if (!response.ok) throw new Error('Failed to generate ideas');
      
      const ideas = await response.json();
      setState(prev => ({ ...prev, ideas, step: 'idea-selection' }));
      
      addBotMessage(
        "I've generated some creative concepts for your ad! Please review and select the one you'd like to use:",
        <div className="space-y-4">
          {ideas.map((idea: any, index: number) => (
            <IdeaCard
              key={index}
              idea={idea}
              onSelect={() => handleIdeaSelect(idea)}
            />
          ))}
        </div>
      );
    } catch (error) {
      addBotMessage("Sorry, I encountered an error while generating ideas. Please try again.");
      setState(prev => ({ ...prev, step: 'url-input' }));
    }
  };

  const handleIdeaSelect = (idea: any) => {
    console.log('handleIdeaSelect called with:', idea);
    selectedIdeaRef.current = idea;
    setState(prev => ({ ...prev, selectedIdea: idea, step: 'duration-selection' }));
    addUserMessage(`Selected: ${idea.summary}`);
    
    addBotMessage(
      "Perfect choice! How long would you like your video to be?",
      <ParameterSelectors
        type="duration"
        onSelect={(duration) => handleDurationSelect(duration as number)}
      />
    );
  };

  const handleDurationSelect = (duration: number) => {
    console.log('handleDurationSelect called with:', duration);
    durationRef.current = duration;
    setState(prev => ({ ...prev, duration, step: 'voice-selection' }));
    addUserMessage(`${duration} seconds`);
    
    addBotMessage(
      "Great! Now let's choose a voice for your ad:",
      <ParameterSelectors
        type="voice"
        onSelect={(voice) => handleVoiceSelect(voice as any)}
      />
    );
  };

  const handleVoiceSelect = (voice: any) => {
    console.log('handleVoiceSelect called with:', voice);
    voiceRef.current = voice;
    setState(prev => ({ ...prev, voice, step: 'aspect-ratio-selection' }));
    addUserMessage(`Selected voice: ${voice.name}`);
    
    addBotMessage(
      "Excellent! What aspect ratio would you like for your video?",
      <ParameterSelectors
        type="aspectRatio"
        onSelect={(aspectRatio) => handleAspectRatioSelect(aspectRatio as any)}
      />
    );
  };

  const handleAspectRatioSelect = async (aspectRatio: any) => {
    console.log('handleAspectRatioSelect called with:', aspectRatio);
    aspectRatioRef.current = aspectRatio;
    console.log('Ref values:', {
      selectedIdea: selectedIdeaRef.current,
      duration: durationRef.current,
      voice: voiceRef.current,
      aspectRatio: aspectRatioRef.current
    });
    
    setState(prev => ({ ...prev, aspectRatio, step: 'script-generation' }));
    addUserMessage(`Format: ${aspectRatio}`);
    
    addBotMessage("Perfect! Now I'll generate a script based on your selections...");
    
    // Use ref values instead of state
    const selectedIdea = selectedIdeaRef.current;
    const duration = durationRef.current;
    
    // Ensure we have the required data
    if (!selectedIdea || !duration) {
      addBotMessage("Error: Missing required information. Please start over.");
      console.error('Missing data from refs:', { selectedIdea, duration });
      return;
    }
    
    try {
      console.log('Sending script generation request:', {
        idea: selectedIdea,
        duration: duration
      });
      
      const response = await fetch('/api/v1/scripts/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          idea: selectedIdea,
          duration: duration,
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        console.error('Script generation error:', errorData);
        throw new Error('Failed to generate script');
      }
      
      const script = await response.json();
      scriptRef.current = script;
      setState(prev => ({ ...prev, script, step: 'script-editing' }));
      
      addBotMessage(
        "Here's your generated script! You can edit any part of it:",
        <ScriptEditor
          script={script}
          onUpdate={(updatedScript) => {
            scriptRef.current = updatedScript;
            setState(prev => ({ ...prev, script: updatedScript }));
          }}
          onNext={() => handleScriptComplete()}
        />
      );
    } catch (error) {
      console.error('Script generation error:', error);
      addBotMessage("Sorry, I encountered an error while generating the script. Please try again.");
    }
  };

  const handleScriptComplete = () => {
    setState(prev => ({ ...prev, step: 'final-review' }));
    addUserMessage("Script looks good!");
    
    addBotMessage(
      "Perfect! Here's a summary of your video ad. Ready to generate?",
      <FinalReview
        selectedIdea={selectedIdeaRef.current!}
        duration={durationRef.current!}
        voice={voiceRef.current!}
        aspectRatio={aspectRatioRef.current!}
        script={scriptRef.current!}
        onGenerate={() => handleVideoGeneration()}
      />
    );
  };

  const handleVideoGeneration = async () => {
    setState(prev => ({ ...prev, step: 'video-generation', status: 'pending' }));
    addUserMessage("Generate my video!");
    
    addBotMessage("Exciting! I'm now generating your video ad. This may take a few minutes...");
    
    // Check if we have all required data
    console.log('Full state before video generation:', state);
    console.log('Ref values:', {
      selectedIdea: selectedIdeaRef.current,
      duration: durationRef.current,
      voice: voiceRef.current
    });
    
    const currentScript = scriptRef.current || state.script;
    
    if (!currentScript || !currentScript.scenes || currentScript.scenes.length === 0) {
      addBotMessage("Error: Script is missing. Please try regenerating the script.");
      console.error('Script missing:', { scriptRef: scriptRef.current, stateScript: state.script });
      return;
    }
    
    try {
      const currentAspectRatio = aspectRatioRef.current || state.aspectRatio;
      
      console.log('Video generation data:', {
        script: currentScript,
        voiceId: voiceRef.current?.id,
        aspectRatio: currentAspectRatio,
      });

      const response = await fetch('/api/v1/videos/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          script: currentScript,
          voiceId: voiceRef.current?.id,
          aspectRatio: currentAspectRatio,
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        console.error('Video generation error:', errorData);
        throw new Error(`Failed to start video generation: ${errorData.error || response.statusText}`);
      }
      
      const { jobId } = await response.json();
      setState(prev => ({ ...prev, jobId, status: 'processing' }));
      
      pollVideoStatus(jobId);
    } catch (error) {
      addBotMessage("Sorry, I encountered an error while starting video generation. Please try again.");
      setState(prev => ({ ...prev, status: 'failed' }));
    }
  };

  const pollVideoStatus = async (jobId: string) => {
    const interval = setInterval(async () => {
      try {
        const response = await fetch(`/api/v1/videos/status/${jobId}`);
        const { status, videoUrl } = await response.json();
        
        setState(prev => ({ ...prev, status }));
        
        if (status === 'completed') {
          clearInterval(interval);
          setState(prev => ({ ...prev, videoUrl, step: 'completed' }));
          addBotMessage(
            "🎉 Your video ad is ready! Here it is:",
            <div className="space-y-4">
              <video
                src={videoUrl}
                controls
                className="w-full max-w-md rounded-lg"
              />
              <div className="flex gap-2">
                <button
                  onClick={() => window.open(videoUrl, '_blank')}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                  Download Video
                </button>
                <button
                  onClick={() => window.location.reload()}
                  className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
                >
                  Create Another
                </button>
              </div>
            </div>
          );
        } else if (status === 'failed') {
          clearInterval(interval);
          addBotMessage("Sorry, video generation failed. Please try again.");
        }
      } catch (error) {
        clearInterval(interval);
        addBotMessage("Error checking video status. Please try again.");
      }
    }, 3000);
  };

  const handleUserInput = (e: React.FormEvent) => {
    e.preventDefault();
    if (!inputValue.trim()) return;
    
    addUserMessage(inputValue);
    setInputValue('');
  };

  return (
    <div className="flex flex-col h-[600px]">
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={`flex items-start gap-3 max-w-[80%] ${
                message.sender === 'user' ? 'flex-row-reverse' : 'flex-row'
              }`}
            >
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center ${
                  message.sender === 'user'
                    ? 'bg-purple-600 text-white'
                    : 'bg-gray-200 text-gray-600'
                }`}
              >
                {message.sender === 'user' ? <User size={16} /> : <Bot size={16} />}
              </div>
              <div
                className={`rounded-lg p-3 ${
                  message.sender === 'user'
                    ? 'bg-purple-600 text-white'
                    : 'bg-gray-100 text-gray-900'
                }`}
              >
                <p className="text-sm">{message.text}</p>
                {message.component && (
                  <div className="mt-3">{message.component}</div>
                )}
              </div>
            </div>
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>
      
      {state.step === 'init' && (
        <div className="border-t p-4">
          <form onSubmit={handleUserInput} className="flex gap-2">
            <input
              type="text"
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              placeholder="Type a message..."
              className="flex-1 px-4 py-3 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            />
            <button
              type="submit"
              className="w-12 h-12 bg-purple-600 text-white rounded-full hover:bg-purple-700 flex items-center justify-center transition-colors"
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M2 21l21-9L2 3v7l15 2-15 2v7z"/>
              </svg>
            </button>
          </form>
        </div>
      )}
    </div>
  );
};

export default ChatInterface;