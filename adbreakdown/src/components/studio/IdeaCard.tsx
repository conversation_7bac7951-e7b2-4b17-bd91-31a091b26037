'use client';

import { VideoIdea } from '@/types/studio';
import { ExternalLink } from 'lucide-react';

interface IdeaCardProps {
  idea: VideoIdea;
  onSelect: () => void;
}

const IdeaCard: React.FC<IdeaCardProps> = ({ idea, onSelect }) => {
  return (
    <div className="bg-gray-900 rounded-lg p-6 shadow-lg border border-gray-700 max-w-2xl">
      <div className="space-y-6">
        <div>
          <h3 className="text-xs font-bold text-gray-400 uppercase tracking-wide mb-2">
            SUMMARY
          </h3>
          <p className="text-gray-100 text-sm leading-relaxed">
            {idea.summary}
          </p>
        </div>

        <div className="border-t border-gray-700"></div>

        <div>
          <h3 className="text-xs font-bold text-gray-400 uppercase tracking-wide mb-2">
            HOOK
          </h3>
          <p className="text-gray-100 text-sm font-medium">
            {idea.hook}
          </p>
        </div>

        <div className="border-t border-gray-700"></div>

        <div>
          <h3 className="text-xs font-bold text-gray-400 uppercase tracking-wide mb-2">
            TARGET AUDIENCE
          </h3>
          <div className="flex flex-wrap gap-2">
            {idea.targetAudience.map((audience, index) => (
              <span
                key={index}
                className="px-3 py-1 bg-gray-700 text-gray-200 text-xs rounded-full"
              >
                {audience}
              </span>
            ))}
          </div>
        </div>

        <div className="border-t border-gray-700"></div>

        <div>
          <h3 className="text-xs font-bold text-gray-400 uppercase tracking-wide mb-2">
            USPS
          </h3>
          <ul className="space-y-1">
            {idea.USPs.map((usp, index) => (
              <li key={index} className="text-gray-100 text-sm flex items-start">
                <span className="text-purple-400 mr-2">•</span>
                {usp}
              </li>
            ))}
          </ul>
        </div>
      </div>

      <div className="mt-6 pt-4 border-t border-gray-700">
        <button
          onClick={onSelect}
          className="w-full bg-purple-600 hover:bg-purple-700 text-white font-medium py-3 px-4 rounded-lg transition-colors"
        >
          Use Idea
        </button>
      </div>
    </div>
  );
};

export default IdeaCard;